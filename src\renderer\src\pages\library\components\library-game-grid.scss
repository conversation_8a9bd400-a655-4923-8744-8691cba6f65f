@use "../../../scss/globals.scss";

/**
 * LibraryGameGrid Styles
 *
 * Touch-optimized, responsive grid system for game cards.
 * Designed for seamless experience across desktop, tablet, and handheld devices.
 *
 * Key Features:
 * - Responsive grid with intelligent column sizing
 * - Steam Deck optimizations (1280x800)
 * - Touch-friendly spacing and sizing
 * - Progressive enhancement for different screen sizes
 * - Performance-optimized animations and transitions
 */

.library-game-grid {
  display: grid;
  gap: calc(globals.$spacing-unit * 3.5);
  padding: calc(globals.$spacing-unit * 3);
  padding-bottom: calc(globals.$spacing-unit * 8);
  animation: gridFadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100%;
  box-sizing: border-box;
  grid-auto-rows: max-content;
  overflow: visible;
  width: 100%;
  position: relative;

  // Enhanced visual organization with subtle background pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.015) 0%, transparent 40%),
      radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.015) 0%, transparent 40%);
    pointer-events: none;
    z-index: 0;
  }

  // Compact card size - More games visible (Full Poster Layout)
  &--compact {
    grid-template-columns: repeat(auto-fill, minmax(210px, 1fr));
    gap: calc(globals.$spacing-unit * 2.5);
  }

  // Normal card size - Balanced view (Full Poster Layout)
  &--normal {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: calc(globals.$spacing-unit * 3);
  }

  // Large card size - Detailed view (Full Poster Layout)
  &--large {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: calc(globals.$spacing-unit * 4);
  }

  // Steam Deck optimizations (1280x800) - Enhanced for handheld gaming
  @media (max-width: 1280px) and (max-height: 800px) {
    gap: calc(globals.$spacing-unit * 2.5);
    padding: calc(globals.$spacing-unit * 2.5);
    padding-bottom: calc(globals.$spacing-unit * 6);

    &--compact {
      grid-template-columns: repeat(auto-fill, minmax(190px, 1fr));
      gap: calc(globals.$spacing-unit * 2.25);
    }

    &--normal {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: calc(globals.$spacing-unit * 2.5);
    }

    &--large {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: calc(globals.$spacing-unit * 2.75);
    }
  }

  // Tablet optimizations - Vertical Poster Layout
  @media (max-width: 1024px) {
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2);

    &--compact {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    &--normal {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    &--large {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
  }

  // Mobile optimizations - Vertical Poster Layout
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 1.5);

    &--compact {
      grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
      gap: calc(globals.$spacing-unit * 1.5);
    }

    &--normal {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: calc(globals.$spacing-unit * 1.5);
    }

    &--large {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  // Very small screens - Vertical Poster Layout
  @media (max-width: 480px) {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1);

    &--compact,
    &--normal,
    &--large {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: calc(globals.$spacing-unit * 1);
    }
  }

  &__empty {
    grid-column: 1 / -1;
    text-align: center;
    padding: calc(globals.$spacing-unit * 6);
    color: globals.$body-color;
  }
}

/**
 * LibraryGameCard Styles
 *
 * Touch-optimized game card component with progressive disclosure.
 * Designed for excellent user experience across all device types.
 *
 * Touch Optimizations:
 * - Minimum 48px touch targets for all interactive elements
 * - Steam Deck specific sizing (52px+ targets)
 * - Touch feedback animations and visual states
 * - Simplified interaction model for handheld devices
 *
 * Performance Features:
 * - Hardware-accelerated animations
 * - Optimized hover states for desktop
 * - Efficient image loading and error handling
 * - Smooth transitions and micro-interactions
 */

.library-game-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 450px; // Much taller cards for better content display
  height: 450px; // Fixed height for consistency
  background: rgba(globals.$dark-background-color, 0.8);
  animation: cardSlideIn 0.5s ease-out;
  animation-fill-mode: both;

  // Touch feedback for mobile devices
  @media (hover: none) and (pointer: coarse) {
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }

  // Stagger animation for cards
  @for $i from 1 through 20 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.03s * $i};
    }
  }

  // Enhanced hover effects for desktop
  @media (hover: hover) {
    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.2);

      .library-game-card__image {
        transform: scale(1.05);
      }

      .library-game-card__overlay {
        background: rgba(0, 0, 0, 0.7);
      }

      .library-game-card__actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  // Full background image layout
  &__image-section {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: 20px;
  }

  &__image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  &__image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;

    &--loaded {
      opacity: 1;
    }
  }

  &__image-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  }

  &__loading-spinner {
    width: 32px;
    height: 32px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-top: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__placeholder-icon {
    width: 48px;
    height: 48px;
    opacity: 0.3;
    color: globals.$body-color;
  }

  // Main overlay containing all information
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.4) 40%,
      rgba(0, 0, 0, 0.9) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit * 1.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    border-radius: 20px;
  }

  &__status-badges {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: calc(globals.$spacing-unit * 1);
    flex-wrap: wrap;
  }

  // Right side button group for options and actions
  &__right-buttons {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.75);
  }

  &__status-badge {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
    border-radius: 8px;
    font-size: 11px;
    font-weight: 600;
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.6);
    min-height: 28px; // Touch target minimum
    border: 1px solid rgba(255, 255, 255, 0.2);

    &--installed {
      background: rgba(34, 197, 94, 0.25);
      color: #10b981;
      border-color: rgba(34, 197, 94, 0.5);
      text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
    }

    &--not-installed {
      background: rgba(249, 115, 22, 0.25);
      color: #f59e0b;
      border-color: rgba(249, 115, 22, 0.5);
      text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 32px;
      font-size: 12px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    }
  }

  &__status-text {
    white-space: nowrap;
  }

  &__favorite-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 50%;
    color: #ef4444;
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 36px;
      height: 36px;
    }
  }



  &__playtime-info {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    font-weight: 500;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 13px;
    }
  }

  &__playtime-text {
    white-space: nowrap;
  }

  // Actions toggle button - Touch optimized
  &__actions-toggle {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      transform: scale(1.05);
    }

    &--active {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.4);
      color: #3b82f6;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 40px;
      height: 40px;
    }
  }

  // Content section - Inside overlay at bottom
  &__content-section {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__header {
    display: flex;
    align-items: flex-start;
    gap: calc(globals.$spacing-unit * 0.75);
    margin-bottom: calc(globals.$spacing-unit * 0.75);
  }

  &__shop-badge {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    backdrop-filter: blur(8px);
  }

  &__shop-icon {
    width: 14px;
    height: 14px;
    opacity: 0.9;
  }

  &__title-container {
    flex: 1;
    min-width: 0;
  }

  &__title {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.98);
    line-height: 1.25;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: color 0.3s ease;
    text-shadow: 0 3px 16px rgba(0, 0, 0, 0.9);
    letter-spacing: -0.01em;

    // Enhanced readability
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  &__metadata {
    margin-bottom: calc(globals.$spacing-unit * 1.5);
  }

  &__last-played {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 0.5);
    margin-bottom: calc(globals.$spacing-unit * 1);
  }

  &__last-played-label {
    font-size: 12px;
    color: globals.$body-color;
    opacity: 0.7;
    font-weight: 500;
  }

  &__last-played-date {
    font-size: 13px;
    color: globals.$muted-color;
    font-weight: 600;
  }

  &__genres {
    display: flex;
    flex-wrap: wrap;
    gap: calc(globals.$spacing-unit * 0.5);
  }

  &__genre-tag {
    padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    font-size: 11px;
    color: globals.$body-color;
    font-weight: 500;
  }

  &__genre-more {
    padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 11px;
    color: globals.$body-color;
    opacity: 0.7;
    font-weight: 500;
  }

  // Primary Action Button - Always Visible
  &__primary-action {
    display: flex;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__play-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.5);
    flex: 1;
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.25);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    min-height: 42px; // Touch target minimum
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);

    &--play {
      background: rgba(34, 197, 94, 0.3);
      border-color: rgba(34, 197, 94, 0.6);
      color: #10b981;
      box-shadow: 0 2px 12px rgba(34, 197, 94, 0.4);

      &:hover {
        background: rgba(34, 197, 94, 0.4);
        border-color: rgba(34, 197, 94, 0.8);
        color: #059669;
        transform: translateY(-1px);
        box-shadow: 0 4px 20px rgba(34, 197, 94, 0.5);
      }
    }

    &--download {
      background: rgba(59, 130, 246, 0.3);
      border-color: rgba(59, 130, 246, 0.6);
      color: #2563eb;
      box-shadow: 0 2px 12px rgba(59, 130, 246, 0.4);

      &:hover {
        background: rgba(59, 130, 246, 0.4);
        border-color: rgba(59, 130, 246, 0.8);
        color: #1d4ed8;
        transform: translateY(-1px);
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.5);
      }
    }

    &:active {
      transform: translateY(0);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
      font-size: 18px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }

  &__play-text {
    white-space: nowrap;
    font-weight: 600;
  }

  // Actions panel - Progressive disclosure
  &__actions-panel {
    margin-top: calc(globals.$spacing-unit * 1);
    padding-top: calc(globals.$spacing-unit * 1);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    animation: actionsPanelSlideIn 0.3s ease-out;
  }

  // Actions visibility state
  &--actions-visible {
    .library-game-card__actions-toggle {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.4);
      color: #3b82f6;
    }
  }

  // Hover states - Desktop only
  @media (hover: hover) and (pointer: fine) {
    &:hover,
    &--hovered {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 24px 48px rgba(0, 0, 0, 0.5);

      .library-game-card__image {
        transform: scale(1.05);
      }

      .library-game-card__overlay {
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0.3) 0%,
          rgba(0, 0, 0, 0.5) 40%,
          rgba(0, 0, 0, 0.95) 100%
        );
      }
    }

    &:active {
      transform: translateY(-3px) scale(1.01);
    }
  }

}

// Animations
@keyframes gridFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes actionsPanelSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


