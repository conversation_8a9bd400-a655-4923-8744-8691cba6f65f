import { useCallback, useEffect, useState } from "react";
import { useAppSelector } from "./use-app-selector";

interface ProfileCoverImages {
  [gameKey: string]: string; // gameKey format: "shop:objectId"
}

export function useProfileCoverImages() {
  const [profileCoverImages, setProfileCoverImages] = useState<ProfileCoverImages>({});
  const [isLoading, setIsLoading] = useState(false);
  const { userDetails } = useAppSelector((state) => state.userDetails);

  const fetchProfileCoverImages = useCallback(async () => {
    if (!userDetails?.id) return;

    setIsLoading(true);
    try {
      const userProfile = await window.electron.getUser(userDetails.id);
      if (userProfile?.libraryGames) {
        const coverImages: ProfileCoverImages = {};
        userProfile.libraryGames.forEach((game) => {
          if (game.coverImageUrl) {
            const key = `${game.shop}:${game.objectId}`;
            coverImages[key] = game.coverImageUrl;
          }
        });
        setProfileCoverImages(coverImages);
      }
    } catch (error) {
      console.warn("Failed to fetch profile cover images:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userDetails?.id]);

  useEffect(() => {
    fetchProfileCoverImages();
  }, [fetchProfileCoverImages]);

  const getCoverImageUrl = useCallback(
    (shop: string, objectId: string): string | undefined => {
      const key = `${shop}:${objectId}`;
      return profileCoverImages[key];
    },
    [profileCoverImages]
  );

  return {
    getCoverImageUrl,
    isLoading,
    refetch: fetchProfileCoverImages,
  };
}
