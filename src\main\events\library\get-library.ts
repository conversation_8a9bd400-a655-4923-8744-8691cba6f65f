import type { LibraryGame, UserProfile } from "@types";
import { registerEvent } from "../register-event";
import {
  downloadsSublevel,
  gamesShopAssetsSublevel,
  gamesSublevel,
} from "@main/level";
import { HydraApi } from "@main/services";
import { getUserData } from "@main/services/user/get-user-data";

// Helper function to get cover images from user profile
const getProfileCoverImages = async (): Promise<Map<string, string>> => {
  const coverImages = new Map<string, string>();

  try {
    const userDetails = await getUserData();
    if (userDetails?.id) {
      const userProfile = await HydraApi.get<UserProfile>(`/users/${userDetails.id}`);
      if (userProfile?.libraryGames) {
        userProfile.libraryGames.forEach((userGame) => {
          if (userGame.coverImageUrl) {
            const key = `${userGame.shop}:${userGame.objectId}`;
            coverImages.set(key, userGame.coverImageUrl);
          }
        });
      }
    }
  } catch (error) {
    console.warn("Failed to get cover images from user profile:", error);
  }

  return coverImages;
};

const getLibrary = async (): Promise<LibraryGame[]> => {
  // Get cover images from profile in parallel
  const profileCoverImagesPromise = getProfileCoverImages();

  // Get library data from local storage (original approach)
  const libraryGamesPromise = gamesSublevel
    .iterator()
    .all()
    .then((results) => {
      return Promise.all(
        results
          .filter(([_key, game]) => game.isDeleted === false)
          .map(async ([key, game]) => {
            const download = await downloadsSublevel.get(key);
            const gameAssets = await gamesShopAssetsSublevel.get(key);

            return {
              id: key,
              ...game,
              download: download ?? null,
              ...(gameAssets || {}),
            };
          })
      );
    });

  // Wait for both operations to complete
  const [profileCoverImages, libraryGames] = await Promise.all([
    profileCoverImagesPromise,
    libraryGamesPromise,
  ]);

  // Merge cover images from profile into library games
  return libraryGames.map((game) => {
    const profileCoverImage = profileCoverImages.get(game.id);
    if (profileCoverImage) {
      return {
        ...game,
        coverImageUrl: profileCoverImage,
      };
    }
    return game;
  });
};

registerEvent("getLibrary", getLibrary);
