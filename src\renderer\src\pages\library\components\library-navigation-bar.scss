@use "../../../scss/globals.scss";

.library-navigation-bar {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: calc(globals.$spacing-unit * 3);
  backdrop-filter: blur(32px);
  box-shadow:
    0 12px 48px rgba(0, 0, 0, 0.25),
    0 6px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  margin-bottom: calc(globals.$spacing-unit * 4);
  position: sticky;
  top: calc(globals.$spacing-unit * 2);
  z-index: 15;

  // Enhanced visual prominence
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    border-radius: inherit;
    pointer-events: none;
  }

  // Steam Deck optimizations (1280x800)
  @media (max-width: 1280px) and (max-height: 800px) {
    padding: calc(globals.$spacing-unit * 2);
    margin-bottom: calc(globals.$spacing-unit * 2);
    border-radius: 16px;
    top: calc(globals.$spacing-unit * 2);
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    padding: calc(globals.$spacing-unit * 3.5);
    margin-bottom: calc(globals.$spacing-unit * 3.5);
    border-radius: 20px;
  }

  @media (max-width: 768px) {
    padding: calc(globals.$spacing-unit * 2.5);
    margin-bottom: calc(globals.$spacing-unit * 2.5);
    border-radius: 18px;
  }

  @media (max-width: 480px) {
    padding: calc(globals.$spacing-unit * 2);
    margin-bottom: calc(globals.$spacing-unit * 2);
    border-radius: 16px;
  }

  @media (max-width: 360px) {
    padding: calc(globals.$spacing-unit * 1.5);
    margin-bottom: calc(globals.$spacing-unit * 1.5);
    border-radius: 14px;
  }

  &__main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(globals.$spacing-unit * 3);
    margin-bottom: calc(globals.$spacing-unit * 2);
    position: relative;
    z-index: 1;

    @media (max-width: 1024px) {
      gap: calc(globals.$spacing-unit * 2);
      margin-bottom: calc(globals.$spacing-unit * 1.5);
    }

    @media (max-width: 768px) {
      margin-bottom: calc(globals.$spacing-unit * 1);
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__left {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    flex: 1;
    min-width: 0;

    @media (max-width: 768px) {
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__right {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    flex-shrink: 0;

    @media (max-width: 768px) {
      gap: calc(globals.$spacing-unit * 1);
    }
  }

  // Collections Toggle - Touch Optimized
  &__collections-toggle {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 48px; // Touch target minimum
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
    }

    @media (max-width: 768px) {
      min-height: 44px;
      padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);

      .library-navigation-bar__collections-text {
        display: none;
      }
    }
  }

  &__collections-text {
    font-weight: 600;
    font-size: globals.$body-font-size;
    white-space: nowrap;
  }

  &__search-container {
    flex: 1;
    min-width: 320px;
    max-width: 500px;
    position: relative;

    @media (max-width: 1024px) {
      min-width: 280px;
      max-width: 400px;
    }

    @media (max-width: 768px) {
      min-width: 100%;
      max-width: none;
    }
  }

  &__search {
    width: 100%;
    min-height: 52px; // Enhanced touch target

    // Enhanced visual prominence
    .library-search__input-wrapper {
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(16px);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 56px;
    }

    @media (max-width: 768px) {
      min-height: 48px;
    }
  }



  // Desktop Controls - Hidden on Mobile
  &__desktop-controls {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);

    @media (max-width: 768px) {
      display: none;
    }
  }

  // Mobile Menu Toggle - Only Visible on Mobile
  &__mobile-menu-toggle {
    display: none;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }

    @media (max-width: 768px) {
      display: flex;
    }
  }

  // Mobile Menu
  &__mobile-menu {
    position: absolute;
    top: calc(100% + calc(globals.$spacing-unit * 1));
    right: 0;
    left: 0;
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.95) 0%, rgba(21, 21, 21, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(24px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.4),
      0 6px 24px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 20;
    animation: mobileMenuSlideIn 0.3s ease-out;

    @media (max-width: 768px) {
      display: block;
    }
  }

  &__mobile-menu-content {
    padding: calc(globals.$spacing-unit * 2);
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1.5);
  }

  &__mobile-item {
    position: relative;
  }

  &__mobile-button {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    width: 100%;
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 52px; // Touch target
    font-size: globals.$body-font-size;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }
  }

  &__mobile-label {
    display: block;
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: globals.$body-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: calc(globals.$spacing-unit * 1);
  }

  // Sort Control - Desktop
  &__sort {
    position: relative;
  }

  &__sort-button {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 48px;
    font-size: globals.$body-font-size;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.2);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
    }
  }

  &__sort-text {
    white-space: nowrap;
  }

  &__sort-chevron {
    transition: transform 0.2s ease;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__sort-dropdown {
    position: absolute;
    top: calc(100% + calc(globals.$spacing-unit * 0.5));
    right: 0;
    z-index: 20;
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.95) 0%, rgba(21, 21, 21, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    backdrop-filter: blur(24px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.4),
      0 6px 24px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-width: 200px;
    animation: dropdownSlideIn 0.2s ease-out;
  }

  &__sort-dropdown-header {
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  &__sort-dropdown-title {
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__sort-dropdown-content {
    padding: calc(globals.$spacing-unit * 0.5);
  }

  &__sort-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);
    background: transparent;
    border: 1px solid transparent;
    border-radius: 8px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: calc(globals.$spacing-unit * 0.25);
    min-height: 44px;
    font-size: globals.$body-font-size;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
    }

    &:focus {
      outline: 2px solid rgba(22, 177, 149, 0.4);
      outline-offset: 2px;
    }

    &--selected {
      background: linear-gradient(135deg, rgba(22, 177, 149, 0.15) 0%, rgba(22, 177, 149, 0.1) 100%);
      border: 1px solid rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;

      &:hover {
        background: linear-gradient(135deg, rgba(22, 177, 149, 0.2) 0%, rgba(22, 177, 149, 0.15) 100%);
        color: globals.$brand-teal;
        border-color: rgba(22, 177, 149, 0.4);
      }
    }
  }

  &__sort-option-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__sort-option-text {
    font-weight: 500;
  }

  &__sort-option-check {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: globals.$brand-teal;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__sort-option-check-mark {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: white;
  }

  // Mobile Sort Dropdown
  &__mobile-sort-dropdown {
    margin-top: calc(globals.$spacing-unit * 1);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    overflow: hidden;
    padding: calc(globals.$spacing-unit * 0.5);
  }

  &__mobile-sort-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);
    background: transparent;
    border: 1px solid transparent;
    border-radius: 8px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: calc(globals.$spacing-unit * 0.25);
    min-height: 48px;
    font-size: globals.$body-font-size;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
    }

    &--selected {
      background: linear-gradient(135deg, rgba(22, 177, 149, 0.15) 0%, rgba(22, 177, 149, 0.1) 100%);
      border: 1px solid rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;

      &:hover {
        background: linear-gradient(135deg, rgba(22, 177, 149, 0.2) 0%, rgba(22, 177, 149, 0.15) 100%);
        color: globals.$brand-teal;
        border-color: rgba(22, 177, 149, 0.4);
      }
    }
  }

  &__mobile-sort-option-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-sort-option-check {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: globals.$brand-teal;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__mobile-sort-option-check-mark {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: white;
  }

  // Mobile Quick Filters
  &__mobile-quick-filters {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-filter-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 52px;
    font-size: globals.$body-font-size;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }
  }

  // Mobile View Controls
  &__mobile-view-controls {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-view-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-view-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 52px;
    font-size: globals.$body-font-size;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }
  }

  // Mobile Card Size Controls
  &__mobile-card-size {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-card-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__mobile-card-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.75);
    padding: calc(globals.$spacing-unit * 1.25);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 48px;
    font-size: globals.$small-font-size;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      border-color: rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;
    }
  }

  // Smart Filters Dropdown
  &__filters {
    position: relative;
  }

  &__filters-button {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 52px;
    font-size: 14px;
    font-weight: 600;
    backdrop-filter: blur(16px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
    }

    &--active {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.2);
    }

    &--has-active {
      background: rgba(22, 177, 149, 0.1);
      border-color: rgba(22, 177, 149, 0.25);
      color: globals.$brand-teal;

      &:hover {
        background: rgba(22, 177, 149, 0.15);
        border-color: rgba(22, 177, 149, 0.3);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
    }
  }

  &__filters-text {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    white-space: nowrap;
  }

  &__filters-count {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    background: globals.$brand-teal;
    color: white;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 700;
    padding: 0 calc(globals.$spacing-unit * 0.5);
  }

  &__filters-chevron {
    transition: transform 0.2s ease;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__filters-dropdown {
    position: absolute;
    top: calc(100% + calc(globals.$spacing-unit * 0.5));
    right: 0;
    z-index: 25;
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.98) 0%, rgba(21, 21, 21, 0.98) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(32px);
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.5),
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-width: 320px;
    max-width: 420px;
    max-height: 70vh;
    animation: filtersDropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;

    // Custom scrollbar for the entire dropdown
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
      margin: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &__filters-dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  }

  &__filters-dropdown-title {
    font-size: 16px;
    font-weight: 700;
    color: globals.$body-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__filters-clear-all {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    background: rgba(128, 29, 30, 0.1);
    border: 1px solid rgba(128, 29, 30, 0.3);
    border-radius: 8px;
    color: globals.$danger-color;
    font-size: globals.$small-font-size;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(128, 29, 30, 0.15);
      border-color: rgba(128, 29, 30, 0.4);
      transform: translateY(-1px);
    }
  }

  &__filters-dropdown-content {
    padding: calc(globals.$spacing-unit * 1.5);
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__filters-section {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1);
  }

  &__filters-section-title {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.75);
    font-size: globals.$small-font-size;
    font-weight: 700;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
  }

  &__filters-options {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 0.75);
  }

  &__filter-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: globals.$body-font-size;
    font-weight: 500;
    min-height: 48px;

    &:hover {
      background: rgba(255, 255, 255, 0.06);
      border-color: rgba(255, 255, 255, 0.12);
      transform: translateY(-1px);
    }

    &--active {
      background: linear-gradient(135deg, rgba(22, 177, 149, 0.12) 0%, rgba(22, 177, 149, 0.08) 100%);
      border: 1px solid rgba(22, 177, 149, 0.25);
      color: globals.$brand-teal;

      &:hover {
        background: linear-gradient(135deg, rgba(22, 177, 149, 0.18) 0%, rgba(22, 177, 149, 0.12) 100%);
        border-color: rgba(22, 177, 149, 0.35);
      }
    }

    span {
      display: flex;
      align-items: center;
      gap: calc(globals.$spacing-unit * 0.75);
    }
  }

  &__filter-option-check {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: globals.$brand-teal;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: white;
    }
  }

  &__active-genres-list {
    display: flex;
    flex-wrap: wrap;
    gap: calc(globals.$spacing-unit * 0.75);
  }

  &__genre-tag {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    background: rgba(62, 98, 192, 0.1);
    border: 1px solid rgba(62, 98, 192, 0.25);
    border-radius: 16px;
    color: globals.$brand-blue;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(62, 98, 192, 0.15);
      border-color: rgba(62, 98, 192, 0.35);
    }

    span {
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &__genre-tag-remove {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: transparent;
    border: none;
    border-radius: 50%;
    color: inherit;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }
  }

  // Genres Grid
  &__genres-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: calc(globals.$spacing-unit * 0.75);
  }

  &__genre-chip {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.25);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    color: globals.$muted-color;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    font-weight: 500;
    min-height: 40px;
    position: relative;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      color: globals.$body-color;
      transform: translateY(-1px);
    }

    &--selected {
      background: rgba(255, 255, 255, 0.12);
      border: 1px solid rgba(255, 255, 255, 0.25);
      color: white;

      .library-navigation-bar__genre-chip-icon {
        color: white;
      }

      .library-navigation-bar__genre-chip-count {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.18);
        border-color: rgba(255, 255, 255, 0.35);
        color: white;
      }
    }
  }

  &__genre-chip-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-right: calc(globals.$spacing-unit * 0.5);
    color: globals.$muted-color;
    transition: color 0.2s ease;
  }

  &__genre-chip-text {
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
  }

  &__genre-chip-count {
    font-size: 10px;
    font-weight: 700;
    color: globals.$muted-color;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: calc(globals.$spacing-unit * 0.5);
    min-width: 20px;
    text-align: center;
  }

  &__genre-chip-check {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  &__genre-chip-check-mark {
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
  }

  &__genres-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 2);
    color: globals.$muted-color;
    font-size: globals.$small-font-size;
    grid-column: 1 / -1;
  }

  &__loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-top: 2px solid globals.$brand-teal;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }



  &__no-genres {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 1);
    padding: calc(globals.$spacing-unit * 2);
    color: globals.$muted-color;
    font-size: globals.$small-font-size;
  }



  // View Controls - Desktop
  &__view-controls {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: calc(globals.$spacing-unit * 0.5);
    gap: calc(globals.$spacing-unit * 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  &__view-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      color: globals.$muted-color;
    }

    &--active {
      background: rgba(22, 177, 149, 0.15);
      color: globals.$brand-teal;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 48px;
      height: 48px;
    }
  }
}

// Animations
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes mobileMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-12px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes filtersDropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.96);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Touch feedback for better mobile experience
@media (hover: none) and (pointer: coarse) {
  .library-navigation-bar {
    &__collections-toggle,
    &__mobile-menu-toggle,
    &__sort-button,
    &__filters-button,
    &__view-button,
    &__mobile-button,
    &__mobile-view-button,
    &__mobile-card-button {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }
}
