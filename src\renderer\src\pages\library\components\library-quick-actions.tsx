import { useState, useRef, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  PlayIcon,
  DownloadIcon,
  KebabHorizontalIcon,
  PlusIcon,
  DashIcon,
  LinkExternalIcon,
  FileDirectoryIcon,
  TrashIcon,
  GearIcon,
  HeartIcon,
  HeartFillIcon,
} from "@primer/octicons-react";

import type { LibraryGame, GameRepack } from "@types";
import { useLibraryCollections, useRepacks, useDownload, useLibrary } from "@renderer/hooks";
import { LibraryRepacksModal } from "./library-repacks-modal";
import { Downloader, getDownloadersForUri } from "@shared";

import "./library-quick-actions.scss";

/**
 * LibraryQuickActions Component
 *
 * Touch-optimized quick actions panel for library games.
 * Provides essential game actions with progressive disclosure.
 *
 * Touch Optimizations:
 * - Minimum 48px touch targets for all interactive elements
 * - Steam Deck specific sizing (52px+ targets)
 * - Touch feedback animations and visual states
 * - Simplified interaction model for handheld devices
 * - Progressive disclosure to reduce interface complexity
 *
 * Features:
 * - Primary action button (Play/Download) with clear visual hierarchy
 * - Secondary actions menu with contextual options
 * - Favorite toggle for quick access
 * - Collection management integration
 * - Responsive layout for different card sizes
 *
 * Performance Features:
 * - Memoized event handlers to prevent unnecessary re-renders
 * - Efficient menu state management
 * - Optimized click outside detection
 * - Keyboard navigation support
 */

interface LibraryQuickActionsProps {
  game: LibraryGame;
  onGameClick: (game: LibraryGame) => void;
  onAddToCollection?: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
  onShowGameOptionsModal?: () => void;
  className?: string;
  variant?: "card-panel" | "integrated" | "compact";
}

export function LibraryQuickActions({
  game,
  onGameClick,
  onAddToCollection,
  onRemoveFromCollection,
  onShowGameOptionsModal,
  className,
  variant = "integrated"
}: LibraryQuickActionsProps) {
  const { t } = useTranslation("library");
  const [showMenu, setShowMenu] = useState(false);
  const [showRepacksModal, setShowRepacksModal] = useState(false);
  const [isFavorite, setIsFavorite] = useState(Boolean(game.favorite));
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Update local favorite state when game prop changes
  useEffect(() => {
    setIsFavorite(Boolean(game.favorite));
  }, [game.favorite]);
  const { selectedCollection } = useLibraryCollections();
  const { getRepacksForObjectId } = useRepacks();
  const { startDownload } = useDownload();
  const { updateLibrary } = useLibrary();

  // Game state calculations
  const isInstalled = Boolean(game.executablePath);
  const isInCollection = Boolean(selectedCollection);

  // Smart collections should always show "add" button, not "remove"
  const smartCollectionIds = ["recently-played", "favorites", "installed", "not-played"];
  const isSmartCollection = selectedCollection && smartCollectionIds.includes(selectedCollection);
  const shouldShowRemoveButton = isInCollection && !isSmartCollection;

  const repacks = getRepacksForObjectId(game.objectId);
  const hasRepacks = repacks.length > 0;

  // Optimized event handlers with useCallback for performance
  const handleClickOutside = useCallback((event: MouseEvent | TouchEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      setShowMenu(false);
    }
  }, []);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === "Escape") {
      setShowMenu(false);
      buttonRef.current?.focus();
    }
  }, []);

  // Close menu when clicking outside - supports both mouse and touch
  useEffect(() => {
    if (showMenu) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("touchstart", handleClickOutside);
      document.addEventListener("keydown", handleKeyDown);

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        document.removeEventListener("touchstart", handleClickOutside);
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [showMenu, handleClickOutside, handleKeyDown]);

  // Primary action handler - Play or Download
  const handlePrimaryAction = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.stopPropagation();

    if (isInstalled) {
      // Launch game
      window.electron.openGame(game.shop, game.objectId, game.executablePath!, game.launchOptions);
    } else {
      // Open repacks modal if repacks are available, otherwise go to game page
      if (hasRepacks) {
        setShowRepacksModal(true);
      } else {
        onGameClick(game);
      }
    }
  }, [isInstalled, hasRepacks, game, onGameClick]);

  // Menu toggle handler
  const handleMenuToggle = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  }, [showMenu]);

  // Favorite toggle handler
  const handleFavoriteToggle = useCallback(async (e: React.MouseEvent | React.TouchEvent) => {
    e.stopPropagation();

    try {
      if (isFavorite) {
        await window.electron.removeGameFromFavorites(game.shop, game.objectId);
      } else {
        await window.electron.addGameToFavorites(game.shop, game.objectId);
      }

      // Update local state immediately for responsive UI
      setIsFavorite(!isFavorite);

      // Update library to persist changes and refresh smart collections
      await updateLibrary();
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      // Revert local state on error
      setIsFavorite(isFavorite);
    }
  }, [isFavorite, game, updateLibrary]);

  // Generic menu item click handler
  const handleMenuItemClick = useCallback((action: () => void) => {
    return (e: React.MouseEvent | React.TouchEvent) => {
      e.stopPropagation();
      action();
      setShowMenu(false);
    };
  }, []);

  // Specific action handlers - memoized for performance
  const handleOpenFolder = useCallback(() => {
    if (game.executablePath) {
      window.electron.showItemInFolder(game.executablePath);
    }
  }, [game.executablePath]);

  const handleOpenGamePage = useCallback(() => {
    onGameClick(game);
  }, [onGameClick, game]);

  const handleAddToCollection = useCallback(() => {
    onAddToCollection?.(game);
  }, [onAddToCollection, game]);

  const handleRemoveFromCollection = useCallback(() => {
    onRemoveFromCollection?.(game);
  }, [onRemoveFromCollection, game]);

  const handleRemoveFromLibrary = useCallback(async () => {
    try {
      await window.electron.removeGameFromLibrary(game.shop, game.objectId);
    } catch (error) {
      console.error("Failed to remove game from library:", error);
    }
  }, [game]);

  const selectRepackUri = (repack: GameRepack, downloader: Downloader) =>
    repack.uris.find((uri) => getDownloadersForUri(uri).includes(downloader))!;

  const handleStartDownload = async (
    repack: GameRepack,
    downloader: Downloader,
    downloadPath: string,
    automaticallyExtract: boolean
  ) => {
    const response = await startDownload({
      repackId: repack.id,
      objectId: game.objectId,
      title: game.title,
      downloader,
      shop: game.shop,
      downloadPath,
      uri: selectRepackUri(repack, downloader),
      automaticallyExtract: automaticallyExtract,
    });

    return response;
  };

  // Render different layouts based on variant
  const renderCardPanelLayout = () => (
    <div className="library-quick-actions__card-panel">
      {/* For installed games: Two rows - folder button on top, other buttons below */}
      {isInstalled ? (
        <>
          {/* Top row - Folder and Options buttons */}
          <div className="library-quick-actions__folder-row">
            <button
              type="button"
              className="library-quick-actions__folder-button"
              onClick={handleMenuItemClick(handleOpenFolder)}
              aria-label={t("open_folder")}
            >
              <FileDirectoryIcon size={16} />
              <span className="library-quick-actions__secondary-text">{t("folder")}</span>
            </button>

            {onShowGameOptionsModal && (
              <button
                type="button"
                className="library-quick-actions__options-button"
                onClick={handleMenuItemClick(() => onShowGameOptionsModal())}
                aria-label={t("game_options")}
              >
                <GearIcon size={16} />
                <span className="library-quick-actions__secondary-text">{t("options")}</span>
              </button>
            )}
          </div>

          {/* Bottom row - Favorite and collection buttons */}
          <div className="library-quick-actions__secondary-row">
            <button
              type="button"
              className={`library-quick-actions__favorite-button ${
                isFavorite ? "library-quick-actions__favorite-button--active" : ""
              }`}
              onClick={handleFavoriteToggle}
              aria-label={isFavorite ? t("remove_from_favorites") : t("add_to_favorites")}
            >
              {isFavorite ? <HeartFillIcon size={18} /> : <HeartIcon size={18} />}
              <span className="library-quick-actions__secondary-text">
                {isFavorite ? t("unfavorite") : t("favorite")}
              </span>
            </button>

            {shouldShowRemoveButton && onRemoveFromCollection ? (
              <button
                type="button"
                className="library-quick-actions__secondary-button library-quick-actions__secondary-button--danger"
                onClick={handleMenuItemClick(handleRemoveFromCollection)}
                aria-label={t("remove_from_collection")}
              >
                <DashIcon size={16} />
                <span className="library-quick-actions__secondary-text">{t("remove")}</span>
              </button>
            ) : onAddToCollection ? (
              <button
                type="button"
                className="library-quick-actions__secondary-button"
                onClick={handleMenuItemClick(handleAddToCollection)}
                aria-label={t("add_to_collection")}
              >
                <PlusIcon size={16} />
                <span className="library-quick-actions__secondary-text">{t("add")}</span>
              </button>
            ) : null}
          </div>
        </>
      ) : (
        /* For non-installed games: Single row with favorite and collection buttons */
        <div className="library-quick-actions__secondary-row">
          <button
            type="button"
            className={`library-quick-actions__favorite-button ${
              isFavorite ? "library-quick-actions__favorite-button--active" : ""
            }`}
            onClick={handleFavoriteToggle}
            aria-label={isFavorite ? t("remove_from_favorites") : t("add_to_favorites")}
          >
            {isFavorite ? <HeartFillIcon size={18} /> : <HeartIcon size={18} />}
            <span className="library-quick-actions__secondary-text">
              {isFavorite ? t("unfavorite") : t("favorite")}
            </span>
          </button>

          {shouldShowRemoveButton && onRemoveFromCollection ? (
            <button
              type="button"
              className="library-quick-actions__secondary-button library-quick-actions__secondary-button--danger"
              onClick={handleMenuItemClick(handleRemoveFromCollection)}
              aria-label={t("remove_from_collection")}
            >
              <DashIcon size={16} />
              <span className="library-quick-actions__secondary-text">{t("remove")}</span>
            </button>
          ) : onAddToCollection ? (
            <button
              type="button"
              className="library-quick-actions__secondary-button"
              onClick={handleMenuItemClick(handleAddToCollection)}
              aria-label={t("add_to_collection")}
            >
              <PlusIcon size={16} />
              <span className="library-quick-actions__secondary-text">{t("add")}</span>
            </button>
          ) : null}

          {/* Options button for non-installed games */}
          {onShowGameOptionsModal && (
            <button
              type="button"
              className="library-quick-actions__options-button"
              onClick={handleMenuItemClick(() => onShowGameOptionsModal())}
              aria-label={t("game_options")}
            >
              <GearIcon size={16} />
              <span className="library-quick-actions__secondary-text">{t("options")}</span>
            </button>
          )}
        </div>
      )}
    </div>
  );

  const renderIntegratedLayout = () => (
    <div className="library-quick-actions__integrated">
      {/* Primary Action Button */}
      <button
        type="button"
        className="library-quick-actions__primary-button"
        onClick={handlePrimaryAction}
        aria-label={isInstalled ? t("play") : t("download")}
      >
        {isInstalled ? <PlayIcon size={18} /> : <DownloadIcon size={18} />}
        <span className="library-quick-actions__primary-text">
          {isInstalled ? t("play") : t("download")}
        </span>
      </button>

      {/* Menu Container */}
      <div className="library-quick-actions__menu-container" ref={menuRef}>
        <button
          ref={buttonRef}
          type="button"
          className={`library-quick-actions__menu-button ${
            showMenu ? "library-quick-actions__menu-button--active" : ""
          }`}
          onClick={handleMenuToggle}
          aria-expanded={showMenu}
          aria-haspopup="true"
          aria-label={t("more_actions")}
        >
          <KebabHorizontalIcon size={18} />
        </button>

        {/* Touch-Optimized Menu */}
        {showMenu && (
          <div
            className="library-quick-actions__menu"
            role="menu"
          >
            {/* Favorite Toggle */}
            <button
              type="button"
              className={`library-quick-actions__menu-item ${
                isFavorite ? "library-quick-actions__menu-item--active" : ""
              }`}
              onClick={handleMenuItemClick(handleFavoriteToggle)}
              role="menuitem"
            >
              {isFavorite ? <HeartFillIcon size={16} /> : <HeartIcon size={16} />}
              <span>{isFavorite ? t("remove_from_favorites") : t("add_to_favorites")}</span>
            </button>



            {/* Open Folder */}
            {isInstalled && (
              <button
                type="button"
                className="library-quick-actions__menu-item"
                onClick={handleMenuItemClick(handleOpenFolder)}
                role="menuitem"
              >
                <FileDirectoryIcon size={16} />
                <span>{t("open_folder")}</span>
              </button>
            )}

            <div className="library-quick-actions__menu-divider" />

            {/* Collection Management */}
            {shouldShowRemoveButton && onRemoveFromCollection ? (
              <button
                type="button"
                className="library-quick-actions__menu-item library-quick-actions__menu-item--danger"
                onClick={handleMenuItemClick(handleRemoveFromCollection)}
                role="menuitem"
              >
                <DashIcon size={16} />
                <span>{t("remove_from_collection")}</span>
              </button>
            ) : onAddToCollection ? (
              <button
                type="button"
                className="library-quick-actions__menu-item"
                onClick={handleMenuItemClick(handleAddToCollection)}
                role="menuitem"
              >
                <PlusIcon size={16} />
                <span>{t("add_to_collection")}</span>
              </button>
            ) : null}

            <div className="library-quick-actions__menu-divider" />

            {/* Remove from Library */}
            <button
              type="button"
              className="library-quick-actions__menu-item library-quick-actions__menu-item--danger"
              onClick={handleMenuItemClick(handleRemoveFromLibrary)}
              role="menuitem"
            >
              <TrashIcon size={16} />
              <span>{t("remove_from_library")}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className={`library-quick-actions library-quick-actions--${variant} ${className || ""}`}>
      {variant === "card-panel" ? renderCardPanelLayout() : renderIntegratedLayout()}

      {/* Repacks Modal */}
      <LibraryRepacksModal
        visible={showRepacksModal}
        repacks={repacks}
        game={game}
        startDownload={handleStartDownload}
        onClose={() => setShowRepacksModal(false)}
      />
    </div>
  );
}
