import { useCallback, useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  ClockIcon,
  DownloadIcon,
  CheckIcon,
  PlayIcon,
  HeartIcon,
  HeartFillIcon,
  KebabHorizontalIcon,
  GearIcon,
} from "@primer/octicons-react";

import { buildGameDetailsPath } from "@renderer/helpers";
import { useFormat, useLibraryCollections, useRepacks, useDownload } from "@renderer/hooks";
import type { LibraryGame, LibraryCardSize, GameRepack } from "@types";
import { LibraryQuickActions } from "./library-quick-actions";
import { LibraryRepacksModal } from "./library-repacks-modal";
import { GameOptionsModal } from "@renderer/pages/game-details/modals/game-options-modal";
import { Downloader, getDownloadersForUri } from "@shared";

import SteamLogo from "@renderer/assets/steam-logo.svg?react";

import "./library-game-grid.scss";

/**
 * LibraryGameGrid Component
 *
 * A touch-optimized, responsive grid component for displaying library games.
 * Designed for seamless experience across desktop, tablet, and handheld devices
 * including Steam Deck (1280x800).
 *
 * Features:
 * - Touch-friendly game cards with minimum 48px touch targets
 * - Responsive grid layout that adapts to screen size
 * - Steam Deck optimizations with larger touch targets (52px+)
 * - Progressive disclosure of game actions and information
 * - Smooth animations and hover states for desktop
 * - Accessibility support with proper ARIA labels
 *
 * Performance Optimizations:
 * - Virtualization-ready structure for large libraries
 * - Optimized re-renders with memoized components
 * - Lazy loading of game images and metadata
 * - Efficient event handling with useCallback
 *
 * Future Enhancements:
 * - Gamepad navigation support for Steam Deck
 * - Drag and drop for collection management
 * - Advanced filtering and sorting animations
 * - Context menu integration for quick actions
 */

interface LibraryGameGridProps {
  games: LibraryGame[];
  cardSize?: LibraryCardSize;
  onAddToCollection?: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
}

interface GameCardProps {
  game: LibraryGame;
  cardSize?: LibraryCardSize;
  onGameClick: (game: LibraryGame) => void;
  onAddToCollection: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
  onShowRepacksModal?: () => void;
  onShowGameOptionsModal?: () => void;
}

/**
 * GameCard Component
 *
 * Individual game card optimized for touch interaction and responsive design.
 * Provides progressive disclosure of game information and actions.
 *
 * Touch Optimizations:
 * - Minimum 48px touch targets for all interactive elements
 * - Steam Deck specific sizing (52px+ targets)
 * - Touch feedback animations and visual states
 * - Simplified interaction model for handheld devices
 *
 * Responsive Design:
 * - Adaptive layout based on card size and screen dimensions
 * - Progressive disclosure of secondary information
 * - Optimized typography and spacing for readability
 *
 * Performance Features:
 * - Lazy loading of game images
 * - Memoized calculations and event handlers
 * - Efficient hover state management
 * - Optimized re-render cycles
 */
function GameCard({ game, cardSize = "normal", onGameClick, onAddToCollection, onRemoveFromCollection, onShowRepacksModal, onShowGameOptionsModal }: GameCardProps) {
  const { t } = useTranslation("library");
  const { numberFormatter } = useFormat();
  const [isHovered, setIsHovered] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const { getRepacksForObjectId } = useRepacks();

  // Reset image state when game changes
  useEffect(() => {
    setImageLoaded(false);
    setImageError(false);
  }, [game.coverImageUrl, game.objectId]);

  // Game state calculations
  const repacks = getRepacksForObjectId(game.objectId);
  const hasRepacks = repacks.length > 0;

  // Optimized playtime formatting for better readability
  const formatPlayTime = useCallback(
    (playTimeInMilliseconds: number) => {
      if (!playTimeInMilliseconds) return "0m";

      const hours = Math.floor(playTimeInMilliseconds / (1000 * 60 * 60));
      const minutes = Math.floor((playTimeInMilliseconds % (1000 * 60 * 60)) / (1000 * 60));

      if (hours > 0) {
        return `${numberFormatter.format(hours)}h ${minutes}m`;
      }
      return `${minutes}m`;
    },
    [numberFormatter]
  );

  // Game status helpers
  const isGamePlayable = useCallback(
    (game: LibraryGame) => Boolean(game.executablePath),
    []
  );

  const isGameFavorite = useCallback(
    (game: LibraryGame) => Boolean(game.favorite),
    []
  );

  // Touch-friendly event handlers
  const handleCardClick = useCallback(() => {
    onGameClick(game);
  }, [game, onGameClick]);

  const handleActionsToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setShowActions(!showActions);
  }, [showActions]);

  const handleGameOptions = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onShowGameOptionsModal?.();
  }, [onShowGameOptionsModal]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  // Primary action handler - Play or Download
  const handlePrimaryAction = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    if (isGamePlayable(game)) {
      // Launch game
      window.electron.openGame(game.shop, game.objectId, game.executablePath!, game.launchOptions);
    } else {
      // Open repacks modal if repacks are available, otherwise go to game page
      if (hasRepacks && onShowRepacksModal) {
        onShowRepacksModal();
      } else {
        onGameClick(game);
      }
    }
  }, [game, isGamePlayable, hasRepacks, onGameClick, onShowRepacksModal]);



  return (
    <article
      className={`library-game-card library-game-card--${cardSize} ${
        isHovered ? 'library-game-card--hovered' : ''
      } ${showActions ? 'library-game-card--actions-visible' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="button"
      tabIndex={0}
      aria-label={`${game.title} - ${isGamePlayable(game) ? t("installed") : t("not_installed")}`}
      onClick={handleCardClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick();
        }
      }}
    >
      {/* Full Background Image Section */}
      <div className="library-game-card__image-section">
        <div className="library-game-card__image-container">
          <div className="library-game-card__image-wrapper">
            {game.coverImageUrl && !imageError ? (
              <img
                src={game.coverImageUrl}
                alt={game.title}
                className={`library-game-card__image ${
                  imageLoaded ? 'library-game-card__image--loaded' : ''
                }`}
                loading="lazy"
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            ) : (
              <div className="library-game-card__placeholder">
                <SteamLogo className="library-game-card__placeholder-icon" />
              </div>
            )}

            {/* Loading state */}
            {!imageLoaded && !imageError && game.coverImageUrl && (
              <div className="library-game-card__image-loading">
                <div className="library-game-card__loading-spinner" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Overlay with all information */}
      <div className="library-game-card__overlay">
        {/* Top Section - Status badges and actions toggle */}
        <div className="library-game-card__status-badges">
          {/* Installation Status Badge */}
          <div
            className={`library-game-card__status-badge ${
              isGamePlayable(game)
                ? 'library-game-card__status-badge--installed'
                : 'library-game-card__status-badge--not-installed'
            }`}
          >
            {isGamePlayable(game) ? (
              <>
                <CheckIcon size={14} />
                <span className="library-game-card__status-text">{t("installed")}</span>
              </>
            ) : (
              <>
                <DownloadIcon size={14} />
                <span className="library-game-card__status-text">{t("not_installed")}</span>
              </>
            )}
          </div>

          {/* Favorite Badge */}
          {isGameFavorite(game) && (
            <div className="library-game-card__favorite-badge">
              <HeartFillIcon size={16} />
            </div>
          )}

          {/* Game Options Button */}
          <button
            type="button"
            className="library-game-card__options-button"
            onClick={handleGameOptions}
            aria-label={t("game_options")}
            title={t("game_options")}
          >
            <GearIcon size={18} />
          </button>

          {/* Actions Toggle Button */}
          <button
            type="button"
            className={`library-game-card__actions-toggle ${
              showActions ? 'library-game-card__actions-toggle--active' : ''
            }`}
            onClick={handleActionsToggle}
            aria-label={t("game_actions")}
            aria-expanded={showActions}
          >
            <KebabHorizontalIcon size={20} />
          </button>
        </div>

        {/* Bottom Section - Game info and actions */}
        <div className="library-game-card__content-section">
          {/* Game Title */}
          <div className="library-game-card__header">
            {game.shop === "steam" && (
              <div className="library-game-card__shop-badge">
                <SteamLogo className="library-game-card__shop-icon" />
              </div>
            )}
            <div className="library-game-card__title-container">
              <h3 className="library-game-card__title" title={game.title}>
                {game.title}
              </h3>
            </div>
          </div>

          {/* Playtime Info - Always shown */}
          <div className="library-game-card__playtime-info">
            <ClockIcon size={14} />
            <span className="library-game-card__playtime-text">
              {formatPlayTime(game.playTimeInMilliseconds || 0)}
            </span>
          </div>

          {/* Primary Action Buttons */}
          <div className="library-game-card__primary-action">
            <button
              type="button"
              className={`library-game-card__play-button ${
                isGamePlayable(game)
                  ? 'library-game-card__play-button--play'
                  : 'library-game-card__play-button--download'
              }`}
              onClick={handlePrimaryAction}
              aria-label={isGamePlayable(game) ? t("play") : t("download")}
            >
              {isGamePlayable(game) ? <PlayIcon size={16} /> : <DownloadIcon size={16} />}
              <span className="library-game-card__play-text">
                {isGamePlayable(game) ? t("play") : t("download")}
              </span>
            </button>
          </div>

          {/* Secondary Actions - Progressive Disclosure */}
          {showActions && (
            <div className="library-game-card__actions-panel">
              <LibraryQuickActions
                game={game}
                onGameClick={onGameClick}
                onAddToCollection={onAddToCollection}
                onRemoveFromCollection={onRemoveFromCollection}
                variant="card-panel"
              />
            </div>
          )}
        </div>
      </div>
    </article>
  );
}

// Separate component for the modal to avoid event propagation issues
function GameCardWithModal({ game, cardSize = "normal", onGameClick, onAddToCollection, onRemoveFromCollection }: GameCardProps) {
  const [showRepacksModal, setShowRepacksModal] = useState(false);
  const [showGameOptionsModal, setShowGameOptionsModal] = useState(false);
  const { getRepacksForObjectId } = useRepacks();
  const { startDownload } = useDownload();

  const repacks = getRepacksForObjectId(game.objectId);

  // Download handler
  const selectRepackUri = (repack: GameRepack, downloader: Downloader) =>
    repack.uris.find((uri) => getDownloadersForUri(uri).includes(downloader))!;

  const handleStartDownload = async (
    repack: GameRepack,
    downloader: Downloader,
    downloadPath: string,
    automaticallyExtract: boolean
  ) => {
    const response = await startDownload({
      repackId: repack.id,
      objectId: game.objectId,
      title: game.title,
      downloader,
      shop: game.shop,
      downloadPath,
      uri: selectRepackUri(repack, downloader),
      automaticallyExtract: automaticallyExtract,
    });

    return response;
  };

  return (
    <>
      <GameCard
        game={game}
        cardSize={cardSize}
        onGameClick={onGameClick}
        onAddToCollection={onAddToCollection}
        onRemoveFromCollection={onRemoveFromCollection}
        onShowRepacksModal={() => setShowRepacksModal(true)}
        onShowGameOptionsModal={() => setShowGameOptionsModal(true)}
      />

      {/* Repacks Modal - Rendered outside the card to avoid event conflicts */}
      {showRepacksModal && (
        <LibraryRepacksModal
          visible={showRepacksModal}
          repacks={repacks}
          game={game}
          startDownload={handleStartDownload}
          onClose={() => setShowRepacksModal(false)}
        />
      )}

      {/* Game Options Modal */}
      {showGameOptionsModal && (
        <GameOptionsModal
          visible={showGameOptionsModal}
          game={game}
          onClose={() => setShowGameOptionsModal(false)}
        />
      )}
    </>
  );
}

export function LibraryGameGrid({ games, cardSize = "normal", onAddToCollection, onRemoveFromCollection }: LibraryGameGridProps) {
  const navigate = useNavigate();

  if (games.length === 0) {
    return null; // Empty state is handled by parent component
  }

  const handleGameClick = useCallback(
    (game: LibraryGame) => {
      navigate(buildGameDetailsPath(game));
    },
    [navigate]
  );

  const handleAddToCollection = useCallback(
    (game: LibraryGame) => {
      onAddToCollection?.(game);
    },
    [onAddToCollection]
  );

  const handleRemoveFromCollection = useCallback(
    (game: LibraryGame) => {
      onRemoveFromCollection?.(game);
    },
    [onRemoveFromCollection]
  );

  return (
    <div className={`library-game-grid library-game-grid--${cardSize}`}>
      {games.map((game) => (
        <GameCardWithModal
          key={game.id}
          game={game}
          cardSize={cardSize}
          onGameClick={handleGameClick}
          onAddToCollection={handleAddToCollection}
          onRemoveFromCollection={handleRemoveFromCollection}
        />
      ))}
    </div>
  );
}
